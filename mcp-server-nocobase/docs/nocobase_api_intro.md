当用户创建了名为 `students` 的 collection 后，NocoBase 会自动为该集合生成一套标准的 Swagger API 端点。<cite/>

## 基础 CRUD API 端点

根据 NocoBase 的 API 文档生成机制，`students` collection 会提供以下标准 API： [1](#1-0) 

### 查询操作
- **`/students:list`** - 获取学生列表，支持分页、过滤、排序等参数 [2](#1-1) 
- **`/students:get`** - 获取单个学生记录 [3](#1-2) 

### 写入操作（如果不是只读集合）
- **`/students:create`** - 创建新的学生记录 [4](#1-3) 
- **`/students:update`** - 更新学生记录 [5](#1-4) 
- **`/students:destroy`** - 删除学生记录 [6](#1-5) 

### 排序操作（如果包含排序字段）
如果 `students` collection 包含 `sort` 类型的字段，还会生成：
- **`/students:move`** - 移动记录位置 [7](#1-6) 

## API 参数和响应格式

每个 API 端点都包含标准的查询参数，例如 `get` 操作支持： [8](#1-7) 

- `filterByTk` - 按主键过滤
- `filter` - 条件过滤
- `sort` - 排序
- `fields` - 选择字段
- `appends` - 关联数据
- `except` - 排除字段

## 关联资源 API

如果 `students` collection 包含关联字段（如与 `classes` 的关联），系统还会自动生成关联资源的 API 端点。 [9](#1-8) 

## Schema 定义

系统会为 `students` collection 自动生成对应的 JSON Schema 定义，包括： [10](#1-9) 

- `students` - 完整的数据模型 schema
- `students.form` - 用于创建/更新操作的表单 schema

## Notes

这些 API 端点都遵循 NocoBase 的资源化 RESTful 架构，通过 `@nocobase/plugin-api-doc` 插件自动生成 Swagger 文档。API 的具体可用性取决于集合的配置（如是否为只读视图）和字段类型（如是否包含排序字段）。

基于您的问题，我将详细说明 NocoBase 中 `students` collection 的各种 API 请求参数和输入输出样例。

## 1. 各种请求的参数详情

### Create 操作参数
`/students:create` 支持以下参数： [1](#2-0) 

- `whitelist` - 字段白名单，指定允许修改的字段
- `blacklist` - 字段黑名单，指定禁止修改的字段

### Update 操作参数
`/students:update` 支持以下参数： [2](#2-1) 

- `filterByTk` - 按主键过滤要更新的记录
- `filter` - 条件过滤器
- `whitelist` - 字段白名单
- `blacklist` - 字段黑名单

### Destroy 操作参数
`/students:destroy` 支持以下参数： [3](#2-2) 

- `filterByTk` - 按主键过滤要删除的记录
- `filter` - 条件过滤器

### Move 操作参数
如果 `students` collection 包含排序字段，`/students:move` 支持以下参数： [4](#2-3) 

- `sourceId` - 源记录ID
- `targetId` - 目标记录ID
- `method` - 移动方法（insertAfter 或 insertBefore）
- `sortField` - 排序字段名（默认为 sort）
- `targetScope` - 目标范围
- `sticky` - 是否置顶

## 2. 输入输出样例

### Create 请求样例
**请求体：** [5](#2-4) 
```json
{
  "name": "张三",
  "age": 20,
  "email": "<EMAIL>"
}
```

**响应样例：** [6](#2-5) 
```json
{
  "data": {
    "id": 1,
    "name": "张三",
    "age": 20,
    "email": "<EMAIL>",
    "createdAt": "2024-01-01T00:00:00.000Z",
    "updatedAt": "2024-01-01T00:00:00.000Z"
  }
}
```

### Update 请求样例
**请求体：** [7](#2-6) 
```json
{
  "age": 21,
  "email": "<EMAIL>"
}
```

**响应样例：** [8](#2-7) 
```json
{
  "data": {
    "id": 1,
    "name": "张三",
    "age": 21,
    "email": "<EMAIL>",
    "updatedAt": "2024-01-02T00:00:00.000Z"
  }
}
```

### Get 请求样例
**查询参数：** [9](#2-8) 
```
GET /students:get?filterByTk=1&fields=name,age&appends=classes
```

**响应样例：** [10](#2-9) 
```json
{
  "data": {
    "id": 1,
    "name": "张三",
    "age": 21,
    "classes": [
      {
        "id": 1,
        "name": "计算机科学"
      }
    ]
  }
}
```

## 关联资源操作样例

### Set 关联操作
对于单一关联（如学生与班级的 belongsTo 关系）： [11](#2-10) 

**请求：**
```
POST /students/1/class:set?tk=2
```

### Remove 关联操作 [12](#2-11) 

**请求：**
```
POST /students/1/class:remove
```

## Notes

这些 API 模板通过 `CreateActionTemplate`、`UpdateActionTemplate` 等函数生成，确保了所有 collection 的 API 接口保持一致性。参数的具体可用性取决于 collection 的配置和字段类型。所有响应都遵循统一的 JSON 格式，包含 `data` 字段来承载实际数据。