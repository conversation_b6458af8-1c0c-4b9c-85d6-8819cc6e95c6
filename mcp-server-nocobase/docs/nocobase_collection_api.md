## 基础 CRUD 端点

### 查询操作
- `GET /{collection}:list` - 获取记录列表
- `GET /{collection}:get` - 获取单条记录

### 写入操作（非只读集合）
- `POST /{collection}:create` - 创建新记录
- `POST /{collection}:update` - 更新记录
- `POST /{collection}:destroy` - 删除记录

### 排序操作（包含 sort 字段时）
- `POST /{collection}:move` - 移动记录位置

## API 参数详情

### 通用查询参数
所有查询操作支持以下参数：

| 参数 | 类型 | 描述 |
|------|------|------|
| `filterByTk` | string | 按主键过滤 |
| `filter` | object | 条件过滤器 |
| `sort` | string[] | 排序字段 |
| `fields` | string[] | 选择返回字段 |
| `appends` | string[] | 包含关联数据 |
| `except` | string[] | 排除字段 |
| `page` | number | 页码（从1开始） |
| `pageSize` | number | 每页记录数 |

### Create 操作参数
- `whitelist` - 字段白名单，指定允许修改的字段
- `blacklist` - 字段黑名单，指定禁止修改的字段

### Update 操作参数
- `filterByTk` - 按主键过滤要更新的记录
- `filter` - 条件过滤器
- `whitelist` - 字段白名单
- `blacklist` - 字段黑名单

### Move 操作参数
- `sourceId` - 源记录ID
- `targetId` - 目标记录ID  
- `method` - 移动方法（insertAfter 或 insertBefore）
- `sortField` - 排序字段名（默认为 sort）
- `targetScope` - 目标范围
- `sticky` - 是否置顶

## 请求/响应示例

### 创建记录
**请求：**
```http
POST /students:create
Content-Type: application/json

{
  "name": "张三",
  "age": 20,
  "email": "<EMAIL>"
}
```

**响应：**
```json
{
  "data": {
    "id": 1,
    "name": "张三", 
    "age": 20,
    "email": "<EMAIL>",
    "createdAt": "2024-01-01T00:00:00.000Z",
    "updatedAt": "2024-01-01T00:00:00.000Z"
  }
}
```

### 获取记录列表
**请求：**
```http
GET /students:list?page=1&pageSize=10&sort[]=name&fields[]=name&fields[]=age&appends[]=classes
```

**响应：**
```json
{
  "data": [
    {
      "id": 1,
      "name": "张三",
      "age": 20,
      "classes": [
        {
          "id": 1,
          "name": "计算机科学"
        }
      ]
    }
  ],
  "meta": {
    "count": 1,
    "page": 1,
    "pageSize": 10,
    "totalPage": 1
  }
}
```

### 更新记录
**请求：**
```http
POST /students:update?filterByTk=1
Content-Type: application/json

{
  "age": 21,
  "email": "<EMAIL>"
}
```

**响应：**
```json
{
  "data": {
    "id": 1,
    "name": "张三",
    "age": 21, 
    "email": "<EMAIL>",
    "updatedAt": "2024-01-02T00:00:00.000Z"
  }
}
```

### 删除记录
**请求：**
```http
POST /students:destroy?filterByTk=1
```

**响应：**
```json
{
  "data": {
    "id": 1
  }
}
```

## 关联资源 API

对于包含关联字段的 collection，系统会生成关联操作端点：

### 设置关联
```http
POST /students/1/class:set?tk=2
```

### 移除关联  
```http
POST /students/1/class:remove
```

### 添加关联
```http
POST /students/1/classes:add?tk=2
```

## 错误响应格式

```json
{
  "errors": [
    {
      "message": "错误信息描述"
    }
  ]
}
```

## 认证

所有 API 请求需要在 Header 中包含认证信息：

```http
Authorization: Bearer <your-token>
```

## Swagger 文档

NocoBase 通过 `@nocobase/plugin-api-doc` 插件自动生成 OpenAPI 3.0 规范的 Swagger 文档，可通过以下端点访问：

- `/swagger:getUrls` - 获取所有 API 文档地址
- `/swagger:get` - 获取完整 API 文档
- `/swagger:get?ns=collections/{collection}` - 获取特定 collection 的 API 文档