### 1. 获取现有路由列表
GET https://n.astra.xin/api/desktopRoutes:listAccessible?tree=true
Content-Type: application/json
X-App: mcp_playground
Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ1c2VySWQiOjIsInRlbXAiOnRydWUsImlhdCI6MTc1NDIzNDUwNiwic2lnbkluVGltZSI6MTc1NDIzNDUwNjk2MCwiZXhwIjoxNzU0MzIwOTA2LCJqdGkiOiIyYWE2MTg2NC05MzBhLTRiYmUtYTQzYy1mOTE5ZGEzZGNkMDQifQ.JdiegyE62VL5zg1refeK5MxIL6KI6AjJBELGWmovSm0

###

### 2. 创建一个简单的页面路由
POST https://n.astra.xin/api/desktopRoutes:create
Content-Type: application/json
X-App: mcp_playground
Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ1c2VySWQiOjEsInJvbGVOYW1lIjoiYWRtaW4iLCJpYXQiOjE3NTQyMjk5NjYsImV4cCI6MzMzMTE4Mjk5NjZ9.LFyKzqaVQUCrWf7vxxhQlkvybjX1wmx9w-CZG12zsf0

{
  "type": "page",
  "title": "测试页面",
  "schemaUid": "test-page-uid-001",
  "menuSchemaUid": "test-menu-uid-001"
}

###

### 3. 创建一个带子页面的路由
POST https://n.astra.xin/api/desktopRoutes:create
Content-Type: application/json
X-App: mcp_playground
Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ1c2VySWQiOjEsInJvbGVOYW1lIjoiYWRtaW4iLCJpYXQiOjE3NTQyMjk5NjYsImV4cCI6MzMzMTE4Mjk5NjZ9.LFyKzqaVQUCrWf7vxxhQlkvybjX1wmx9w-CZG12zsf0

{
  "type": "page",
  "title": "学生管理系统",
  "schemaUid": "students-system-uid-001",
  "menuSchemaUid": "students-system-menu-001",
  "enableTabs": true,
  "children": [
    {
      "type": "tab",
      "title": "学生列表",
      "schemaUid": "students-list-tab-001",
      "hidden": false
    }
  ]
}

###

### 4. 获取特定路由详情 (需要先从上面的创建请求中获取ID)
GET https://n.astra.xin/api/desktopRoutes:get?filterByTk=1
Content-Type: application/json
X-App: mcp_playground
Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ1c2VySWQiOjEsInJvbGVOYW1lIjoiYWRtaW4iLCJpYXQiOjE3NTQyMjk5NjYsImV4cCI6MzMzMTE4Mjk5NjZ9.LFyKzqaVQUCrWf7vxxhQlkvybjX1wmx9w-CZG12zsf0

###

### 5. 更新路由 (需要替换实际的ID)
POST https://n.astra.xin/api/desktopRoutes:update?filterByTk=ROUTE_ID_HERE
Content-Type: application/json
X-App: mcp_playground
Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ1c2VySWQiOjEsInJvbGVOYW1lIjoiYWRtaW4iLCJpYXQiOjE3NTQyMjk5NjYsImV4cCI6MzMzMTE4Mjk5NjZ9.LFyKzqaVQUCrWf7vxxhQlkvybjX1wmx9w-CZG12zsf0

{
  "title": "更新后的测试页面"
}

###

### 6. 创建菜单组 (group) 路由
POST https://n.astra.xin/api/desktopRoutes:create
Content-Type: application/json
X-App: mcp_playground
Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ1c2VySWQiOjIsInRlbXAiOnRydWUsImlhdCI6MTc1NDIzNDUwNiwic2lnbkluVGltZSI6MTc1NDIzNDUwNjk2MCwiZXhwIjoxNzU0MzIwOTA2LCJqdGkiOiIyYWE2MTg2NC05MzBhLTRiYmUtYTQzYy1mOTE5ZGEzZGNkMDQifQ.JdiegyE62VL5zg1refeK5MxIL6KI6AjJBELGWmovSm0

{
  "type": "group",
  "title": "系统管理",
  "icon": "SettingOutlined",
  "hideInMenu": false
}

###

### 7. 创建链接 (link) 路由
POST https://n.astra.xin/api/desktopRoutes:create
Content-Type: application/json
X-App: mcp_playground
Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ1c2VySWQiOjIsInRlbXAiOnRydWUsImlhdCI6MTc1NDIzNDUwNiwic2lnbkluVGltZSI6MTc1NDIzNDUwNjk2MCwiZXhwIjoxNzU0MzIwOTA2LCJqdGkiOiIyYWE2MTg2NC05MzBhLTRiYmUtYTQzYy1mOTE5ZGEzZGNkMDQifQ.JdiegyE62VL5zg1refeK5MxIL6KI6AjJBELGWmovSm0

{
  "type": "link",
  "title": "NocoBase 官网",
  "icon": "LinkOutlined",
  "options": {
    "href": "https://www.nocobase.com",
    "openInNewWindow": true
  }
}

###

### 8. 创建带参数的链接路由
POST https://n.astra.xin/api/desktopRoutes:create
Content-Type: application/json
X-App: mcp_playground
Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ1c2VySWQiOjIsInRlbXAiOnRydWUsImlhdCI6MTc1NDIzNDUwNiwic2lnbkluVGltZSI6MTc1NDIzNDUwNjk2MCwiZXhwIjoxNzU0MzIwOTA2LCJqdGkiOiIyYWE2MTg2NC05MzBhLTRiYmUtYTQzYy1mOTE5ZGEzZGNkMDQifQ.JdiegyE62VL5zg1refeK5MxIL6KI6AjJBELGWmovSm0

{
  "type": "link",
  "title": "内部链接",
  "icon": "HomeOutlined",
  "options": {
    "href": "/apps/mcp_playground/admin",
    "params": [
      {
        "name": "tab",
        "value": "settings"
      }
    ],
    "openInNewWindow": false
  }
}

###

### 9. 创建复杂的层级结构
POST https://n.astra.xin/api/desktopRoutes:create
Content-Type: application/json
X-App: mcp_playground
Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ1c2VySWQiOjIsInRlbXAiOnRydWUsImlhdCI6MTc1NDIzNDUwNiwic2lnbkluVGltZSI6MTc1NDIzNDUwNjk2MCwiZXhwIjoxNzU0MzIwOTA2LCJqdGkiOiIyYWE2MTg2NC05MzBhLTRiYmUtYTQzYy1mOTE5ZGEzZGNkMDQifQ.JdiegyE62VL5zg1refeK5MxIL6KI6AjJBELGWmovSm0

{
  "type": "group",
  "title": "业务管理",
  "icon": "AppstoreOutlined",
  "children": [
    {
      "type": "page",
      "title": "用户管理",
      "schemaUid": "users-page-uid-001",
      "menuSchemaUid": "users-menu-uid-001",
      "icon": "UserOutlined",
      "enableTabs": true,
      "children": [
        {
          "type": "tab",
          "title": "用户列表",
          "schemaUid": "users-list-tab-001"
        },
        {
          "type": "tab",
          "title": "用户统计",
          "schemaUid": "users-stats-tab-001"
        }
      ]
    },
    {
      "type": "page",
      "title": "角色管理",
      "schemaUid": "roles-page-uid-001",
      "menuSchemaUid": "roles-menu-uid-001",
      "icon": "TeamOutlined"
    },
    {
      "type": "link",
      "title": "帮助文档",
      "icon": "QuestionCircleOutlined",
      "options": {
        "href": "https://docs.nocobase.com",
        "openInNewWindow": true
      }
    }
  ]
}

###

### 10. 删除路由 (需要替换实际的ID)
POST https://n.astra.xin/api/desktopRoutes:destroy?filterByTk=ROUTE_ID_HERE
Content-Type: application/json
X-App: mcp_playground
Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ1c2VySWQiOjIsInRlbXAiOnRydWUsImlhdCI6MTc1NDIzNDUwNiwic2lnbkluVGltZSI6MTc1NDIzNDUwNjk2MCwiZXhwIjoxNzU0MzIwOTA2LCJqdGkiOiIyYWE2MTg2NC05MzBhLTRiYmUtYTQzYy1mOTE5ZGEzZGNkMDQifQ.JdiegyE62VL5zg1refeK5MxIL6KI6AjJBELGWmovSm0

###
