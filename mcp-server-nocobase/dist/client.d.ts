export interface NocoBaseConfig {
    baseUrl: string;
    token: string;
    app: string;
}
export interface Collection {
    key: string;
    name: string;
    title: string;
    inherit: boolean;
    hidden: boolean;
    description?: string;
    autoGenId: boolean;
    createdAt: boolean;
    updatedAt: boolean;
    createdBy: boolean;
    updatedBy: boolean;
    filterTargetKey: string;
    unavailableActions: string[];
    fields?: Field[];
}
export interface Field {
    key: string;
    name: string;
    type: string;
    interface: string;
    collectionName: string;
    description?: string;
    uiSchema?: any;
    [key: string]: any;
}
export interface Record {
    id: number | string;
    [key: string]: any;
}
export interface DesktopRoute {
    id?: number | string;
    parentId?: number | string;
    type: 'page' | 'tab' | 'group' | 'link';
    title: string;
    icon?: string;
    tooltip?: string;
    schemaUid?: string;
    menuSchemaUid?: string;
    tabSchemaName?: string;
    pageSchemaUid?: string;
    sort?: number;
    options?: {
        href?: string;
        params?: Array<{
            name: string;
            value: any;
        }>;
        openInNewWindow?: boolean;
        [key: string]: any;
    };
    enableTabs?: boolean;
    enableHeader?: boolean;
    displayTitle?: boolean;
    hidden?: boolean;
    hideInMenu?: boolean;
    children?: DesktopRoute[];
    roles?: Array<{
        name: string;
        title: string;
    }>;
    createdAt?: string;
    updatedAt?: string;
    createdBy?: any;
    updatedBy?: any;
    [key: string]: any;
}
export interface ApiResponse<T = any> {
    data: T;
    meta?: {
        count: number;
        page: number;
        pageSize: number;
        totalPage: number;
    };
}
export declare class NocoBaseClient {
    private client;
    private config;
    constructor(config: NocoBaseConfig);
    listCollections(): Promise<Collection[]>;
    listCollectionsMeta(): Promise<Collection[]>;
    getCollection(name: string): Promise<Collection>;
    createCollection(collection: Partial<Collection>): Promise<Collection>;
    updateCollection(name: string, updates: Partial<Collection>): Promise<Collection>;
    deleteCollection(name: string): Promise<void>;
    listFields(collectionName: string): Promise<Field[]>;
    createField(collectionName: string, field: Partial<Field>): Promise<Field>;
    listRecords(collectionName: string, options?: {
        page?: number;
        pageSize?: number;
        filter?: any;
        sort?: string[];
        appends?: string[];
    }): Promise<{
        data: Record[];
        meta?: any;
    }>;
    getRecord(collectionName: string, id: string | number, appends?: string[]): Promise<Record>;
    createRecord(collectionName: string, data: Partial<Record>): Promise<Record>;
    updateRecord(collectionName: string, id: string | number, data: Partial<Record>): Promise<Record>;
    deleteRecord(collectionName: string, id: string | number): Promise<void>;
    listRoutes(options?: {
        tree?: boolean;
    }): Promise<DesktopRoute[]>;
    getRoute(id: string | number): Promise<DesktopRoute>;
    createRoute(route: Partial<DesktopRoute>): Promise<DesktopRoute>;
    updateRoute(id: string | number, updates: Partial<DesktopRoute>): Promise<DesktopRoute>;
    deleteRoute(id: string | number): Promise<void>;
    moveRoute(options: {
        sourceId: string | number;
        targetId?: string | number;
        targetScope?: any;
        sortField?: string;
        sticky?: boolean;
        method?: 'insertAfter' | 'prepend';
    }): Promise<void>;
    createPageSchema(schemaUid: string, schema: any): Promise<any>;
}
//# sourceMappingURL=client.d.ts.map