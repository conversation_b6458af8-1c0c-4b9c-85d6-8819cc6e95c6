import axios, {} from "axios";
export class NocoBaseClient {
    client;
    config;
    constructor(config) {
        this.config = config;
        this.client = axios.create({
            baseURL: config.baseUrl,
            headers: {
                "Authorization": `Bearer ${config.token}`,
                "X-App": config.app,
                "Content-Type": "application/json",
            },
            timeout: 30000,
        });
        // Add response interceptor for error handling
        this.client.interceptors.response.use((response) => response, (error) => {
            if (error.response) {
                const message = error.response.data?.errors?.[0]?.message || error.response.statusText;
                throw new Error(`NocoBase API Error (${error.response.status}): ${message}`);
            }
            else if (error.request) {
                throw new Error("NocoBase API Error: No response received");
            }
            else {
                throw new Error(`NocoBase API Error: ${error.message}`);
            }
        });
    }
    // Collections API
    async listCollections() {
        const response = await this.client.get("/api/collections:list");
        return response.data.data;
    }
    async listCollectionsMeta() {
        const response = await this.client.get("/api/collections:listMeta");
        return response.data.data;
    }
    async getCollection(name) {
        const response = await this.client.get(`/api/collections:get?filterByTk=${name}&appends[]=fields`);
        return response.data.data;
    }
    async createCollection(collection) {
        const response = await this.client.post("/api/collections:create", collection);
        return response.data.data;
    }
    async updateCollection(name, updates) {
        const response = await this.client.post(`/api/collections:update?filterByTk=${name}`, updates);
        return response.data.data;
    }
    async deleteCollection(name) {
        await this.client.post(`/api/collections:destroy?filterByTk=${name}`);
    }
    // Fields API
    async listFields(collectionName) {
        const response = await this.client.get(`/api/collections/${collectionName}/fields:list`);
        return response.data.data;
    }
    async createField(collectionName, field) {
        const response = await this.client.post(`/api/collections/${collectionName}/fields:create`, field);
        return response.data.data;
    }
    // Records API
    async listRecords(collectionName, options) {
        const params = new URLSearchParams();
        if (options?.page)
            params.append("page", options.page.toString());
        if (options?.pageSize)
            params.append("pageSize", options.pageSize.toString());
        if (options?.filter)
            params.append("filter", JSON.stringify(options.filter));
        if (options?.sort) {
            options.sort.forEach(s => params.append("sort[]", s));
        }
        if (options?.appends) {
            options.appends.forEach(a => params.append("appends[]", a));
        }
        const response = await this.client.get(`/api/${collectionName}:list?${params.toString()}`);
        return { data: response.data.data, meta: response.data.meta };
    }
    async getRecord(collectionName, id, appends) {
        const params = new URLSearchParams();
        params.append("filterByTk", id.toString());
        if (appends) {
            appends.forEach(a => params.append("appends[]", a));
        }
        const response = await this.client.get(`/api/${collectionName}:get?${params.toString()}`);
        return response.data.data;
    }
    async createRecord(collectionName, data) {
        const response = await this.client.post(`/api/${collectionName}:create`, data);
        return response.data.data;
    }
    async updateRecord(collectionName, id, data) {
        const response = await this.client.post(`/api/${collectionName}:update?filterByTk=${id}`, data);
        return response.data.data;
    }
    async deleteRecord(collectionName, id) {
        await this.client.post(`/api/${collectionName}:destroy?filterByTk=${id}`);
    }
    // Routes API
    async listRoutes(options) {
        const params = options?.tree ? "?tree=true" : "";
        const response = await this.client.get(`/api/desktopRoutes:listAccessible${params}`);
        return response.data.data;
    }
    async getRoute(id) {
        const response = await this.client.get(`/api/desktopRoutes:get?filterByTk=${id}`);
        return response.data.data;
    }
    async createRoute(route) {
        const response = await this.client.post("/api/desktopRoutes:create", route);
        return response.data.data;
    }
    async updateRoute(id, updates) {
        const response = await this.client.post(`/api/desktopRoutes:update?filterByTk=${id}`, updates);
        return response.data.data;
    }
    async deleteRoute(id) {
        await this.client.post(`/api/desktopRoutes:destroy?filterByTk=${id}`);
    }
    async moveRoute(options) {
        await this.client.post("/api/desktopRoutes:move", options);
    }
    // UI Schema API
    async createPageSchema(schemaUid, schema) {
        const response = await this.client.post("/api/uiSchemas:insert", {
            'x-uid': schemaUid,
            schema: schema
        });
        return response.data.data;
    }
}
//# sourceMappingURL=client.js.map