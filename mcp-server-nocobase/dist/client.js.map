{"version": 3, "file": "client.js", "sourceRoot": "", "sources": ["../src/client.ts"], "names": [], "mappings": "AAAA,OAAO,KAAK,EAAE,EAA0C,MAAM,OAAO,CAAC;AAmFtE,MAAM,OAAO,cAAc;IACjB,MAAM,CAAgB;IACtB,MAAM,CAAiB;IAE/B,YAAY,MAAsB;QAChC,IAAI,CAAC,MAAM,GAAG,MAAM,CAAC;QACrB,IAAI,CAAC,MAAM,GAAG,KAAK,CAAC,MAAM,CAAC;YACzB,OAAO,EAAE,MAAM,CAAC,OAAO;YACvB,OAAO,EAAE;gBACP,eAAe,EAAE,UAAU,MAAM,CAAC,KAAK,EAAE;gBACzC,OAAO,EAAE,MAAM,CAAC,GAAG;gBACnB,cAAc,EAAE,kBAAkB;aACnC;YACD,OAAO,EAAE,KAAK;SACf,CAAC,CAAC;QAEH,8CAA8C;QAC9C,IAAI,CAAC,MAAM,CAAC,YAAY,CAAC,QAAQ,CAAC,GAAG,CACnC,CAAC,QAAQ,EAAE,EAAE,CAAC,QAAQ,EACtB,CAAC,KAAK,EAAE,EAAE;YACR,IAAI,KAAK,CAAC,QAAQ,EAAE,CAAC;gBACnB,MAAM,OAAO,GAAG,KAAK,CAAC,QAAQ,CAAC,IAAI,EAAE,MAAM,EAAE,CAAC,CAAC,CAAC,EAAE,OAAO,IAAI,KAAK,CAAC,QAAQ,CAAC,UAAU,CAAC;gBACvF,MAAM,IAAI,KAAK,CAAC,uBAAuB,KAAK,CAAC,QAAQ,CAAC,MAAM,MAAM,OAAO,EAAE,CAAC,CAAC;YAC/E,CAAC;iBAAM,IAAI,KAAK,CAAC,OAAO,EAAE,CAAC;gBACzB,MAAM,IAAI,KAAK,CAAC,0CAA0C,CAAC,CAAC;YAC9D,CAAC;iBAAM,CAAC;gBACN,MAAM,IAAI,KAAK,CAAC,uBAAuB,KAAK,CAAC,OAAO,EAAE,CAAC,CAAC;YAC1D,CAAC;QACH,CAAC,CACF,CAAC;IACJ,CAAC;IAED,kBAAkB;IAClB,KAAK,CAAC,eAAe;QACnB,MAAM,QAAQ,GAA6C,MAAM,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,uBAAuB,CAAC,CAAC;QAC1G,OAAO,QAAQ,CAAC,IAAI,CAAC,IAAI,CAAC;IAC5B,CAAC;IAED,KAAK,CAAC,mBAAmB;QACvB,MAAM,QAAQ,GAA6C,MAAM,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,2BAA2B,CAAC,CAAC;QAC9G,OAAO,QAAQ,CAAC,IAAI,CAAC,IAAI,CAAC;IAC5B,CAAC;IAED,KAAK,CAAC,aAAa,CAAC,IAAY;QAC9B,MAAM,QAAQ,GAA2C,MAAM,IAAI,CAAC,MAAM,CAAC,GAAG,CAC5E,mCAAmC,IAAI,mBAAmB,CAC3D,CAAC;QACF,OAAO,QAAQ,CAAC,IAAI,CAAC,IAAI,CAAC;IAC5B,CAAC;IAED,KAAK,CAAC,gBAAgB,CAAC,UAA+B;QACpD,MAAM,QAAQ,GAA2C,MAAM,IAAI,CAAC,MAAM,CAAC,IAAI,CAC7E,yBAAyB,EACzB,UAAU,CACX,CAAC;QACF,OAAO,QAAQ,CAAC,IAAI,CAAC,IAAI,CAAC;IAC5B,CAAC;IAED,KAAK,CAAC,gBAAgB,CAAC,IAAY,EAAE,OAA4B;QAC/D,MAAM,QAAQ,GAA2C,MAAM,IAAI,CAAC,MAAM,CAAC,IAAI,CAC7E,sCAAsC,IAAI,EAAE,EAC5C,OAAO,CACR,CAAC;QACF,OAAO,QAAQ,CAAC,IAAI,CAAC,IAAI,CAAC;IAC5B,CAAC;IAED,KAAK,CAAC,gBAAgB,CAAC,IAAY;QACjC,MAAM,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,uCAAuC,IAAI,EAAE,CAAC,CAAC;IACxE,CAAC;IAED,aAAa;IACb,KAAK,CAAC,UAAU,CAAC,cAAsB;QACrC,MAAM,QAAQ,GAAwC,MAAM,IAAI,CAAC,MAAM,CAAC,GAAG,CACzE,oBAAoB,cAAc,cAAc,CACjD,CAAC;QACF,OAAO,QAAQ,CAAC,IAAI,CAAC,IAAI,CAAC;IAC5B,CAAC;IAED,KAAK,CAAC,WAAW,CAAC,cAAsB,EAAE,KAAqB;QAC7D,MAAM,QAAQ,GAAsC,MAAM,IAAI,CAAC,MAAM,CAAC,IAAI,CACxE,oBAAoB,cAAc,gBAAgB,EAClD,KAAK,CACN,CAAC;QACF,OAAO,QAAQ,CAAC,IAAI,CAAC,IAAI,CAAC;IAC5B,CAAC;IAED,cAAc;IACd,KAAK,CAAC,WAAW,CAAC,cAAsB,EAAE,OAMzC;QACC,MAAM,MAAM,GAAG,IAAI,eAAe,EAAE,CAAC;QAErC,IAAI,OAAO,EAAE,IAAI;YAAE,MAAM,CAAC,MAAM,CAAC,MAAM,EAAE,OAAO,CAAC,IAAI,CAAC,QAAQ,EAAE,CAAC,CAAC;QAClE,IAAI,OAAO,EAAE,QAAQ;YAAE,MAAM,CAAC,MAAM,CAAC,UAAU,EAAE,OAAO,CAAC,QAAQ,CAAC,QAAQ,EAAE,CAAC,CAAC;QAC9E,IAAI,OAAO,EAAE,MAAM;YAAE,MAAM,CAAC,MAAM,CAAC,QAAQ,EAAE,IAAI,CAAC,SAAS,CAAC,OAAO,CAAC,MAAM,CAAC,CAAC,CAAC;QAC7E,IAAI,OAAO,EAAE,IAAI,EAAE,CAAC;YAClB,OAAO,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC,CAAC,EAAE,CAAC,MAAM,CAAC,MAAM,CAAC,QAAQ,EAAE,CAAC,CAAC,CAAC,CAAC;QACxD,CAAC;QACD,IAAI,OAAO,EAAE,OAAO,EAAE,CAAC;YACrB,OAAO,CAAC,OAAO,CAAC,OAAO,CAAC,CAAC,CAAC,EAAE,CAAC,MAAM,CAAC,MAAM,CAAC,WAAW,EAAE,CAAC,CAAC,CAAC,CAAC;QAC9D,CAAC;QAED,MAAM,QAAQ,GAAyC,MAAM,IAAI,CAAC,MAAM,CAAC,GAAG,CAC1E,QAAQ,cAAc,SAAS,MAAM,CAAC,QAAQ,EAAE,EAAE,CACnD,CAAC;QACF,OAAO,EAAE,IAAI,EAAE,QAAQ,CAAC,IAAI,CAAC,IAAI,EAAE,IAAI,EAAE,QAAQ,CAAC,IAAI,CAAC,IAAI,EAAE,CAAC;IAChE,CAAC;IAED,KAAK,CAAC,SAAS,CAAC,cAAsB,EAAE,EAAmB,EAAE,OAAkB;QAC7E,MAAM,MAAM,GAAG,IAAI,eAAe,EAAE,CAAC;QACrC,MAAM,CAAC,MAAM,CAAC,YAAY,EAAE,EAAE,CAAC,QAAQ,EAAE,CAAC,CAAC;QAE3C,IAAI,OAAO,EAAE,CAAC;YACZ,OAAO,CAAC,OAAO,CAAC,CAAC,CAAC,EAAE,CAAC,MAAM,CAAC,MAAM,CAAC,WAAW,EAAE,CAAC,CAAC,CAAC,CAAC;QACtD,CAAC;QAED,MAAM,QAAQ,GAAuC,MAAM,IAAI,CAAC,MAAM,CAAC,GAAG,CACxE,QAAQ,cAAc,QAAQ,MAAM,CAAC,QAAQ,EAAE,EAAE,CAClD,CAAC;QACF,OAAO,QAAQ,CAAC,IAAI,CAAC,IAAI,CAAC;IAC5B,CAAC;IAED,KAAK,CAAC,YAAY,CAAC,cAAsB,EAAE,IAAqB;QAC9D,MAAM,QAAQ,GAAuC,MAAM,IAAI,CAAC,MAAM,CAAC,IAAI,CACzE,QAAQ,cAAc,SAAS,EAC/B,IAAI,CACL,CAAC;QACF,OAAO,QAAQ,CAAC,IAAI,CAAC,IAAI,CAAC;IAC5B,CAAC;IAED,KAAK,CAAC,YAAY,CAAC,cAAsB,EAAE,EAAmB,EAAE,IAAqB;QACnF,MAAM,QAAQ,GAAuC,MAAM,IAAI,CAAC,MAAM,CAAC,IAAI,CACzE,QAAQ,cAAc,sBAAsB,EAAE,EAAE,EAChD,IAAI,CACL,CAAC;QACF,OAAO,QAAQ,CAAC,IAAI,CAAC,IAAI,CAAC;IAC5B,CAAC;IAED,KAAK,CAAC,YAAY,CAAC,cAAsB,EAAE,EAAmB;QAC5D,MAAM,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,QAAQ,cAAc,uBAAuB,EAAE,EAAE,CAAC,CAAC;IAC5E,CAAC;IAED,aAAa;IACb,KAAK,CAAC,UAAU,CAAC,OAA4B;QAC3C,MAAM,MAAM,GAAG,OAAO,EAAE,IAAI,CAAC,CAAC,CAAC,YAAY,CAAC,CAAC,CAAC,EAAE,CAAC;QACjD,MAAM,QAAQ,GAA+C,MAAM,IAAI,CAAC,MAAM,CAAC,GAAG,CAChF,oCAAoC,MAAM,EAAE,CAC7C,CAAC;QACF,OAAO,QAAQ,CAAC,IAAI,CAAC,IAAI,CAAC;IAC5B,CAAC;IAED,KAAK,CAAC,QAAQ,CAAC,EAAmB;QAChC,MAAM,QAAQ,GAA6C,MAAM,IAAI,CAAC,MAAM,CAAC,GAAG,CAC9E,qCAAqC,EAAE,EAAE,CAC1C,CAAC;QACF,OAAO,QAAQ,CAAC,IAAI,CAAC,IAAI,CAAC;IAC5B,CAAC;IAED,KAAK,CAAC,WAAW,CAAC,KAA4B;QAC5C,MAAM,QAAQ,GAA6C,MAAM,IAAI,CAAC,MAAM,CAAC,IAAI,CAC/E,2BAA2B,EAC3B,KAAK,CACN,CAAC;QACF,OAAO,QAAQ,CAAC,IAAI,CAAC,IAAI,CAAC;IAC5B,CAAC;IAED,KAAK,CAAC,WAAW,CAAC,EAAmB,EAAE,OAA8B;QACnE,MAAM,QAAQ,GAA6C,MAAM,IAAI,CAAC,MAAM,CAAC,IAAI,CAC/E,wCAAwC,EAAE,EAAE,EAC5C,OAAO,CACR,CAAC;QACF,OAAO,QAAQ,CAAC,IAAI,CAAC,IAAI,CAAC;IAC5B,CAAC;IAED,KAAK,CAAC,WAAW,CAAC,EAAmB;QACnC,MAAM,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,yCAAyC,EAAE,EAAE,CAAC,CAAC;IACxE,CAAC;IAED,KAAK,CAAC,SAAS,CAAC,OAOf;QACC,MAAM,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,yBAAyB,EAAE,OAAO,CAAC,CAAC;IAC7D,CAAC;IAED,gBAAgB;IAChB,KAAK,CAAC,gBAAgB,CAAC,SAAiB,EAAE,MAAW;QACnD,MAAM,QAAQ,GAAoC,MAAM,IAAI,CAAC,MAAM,CAAC,IAAI,CACtE,uBAAuB,EACvB;YACE,OAAO,EAAE,SAAS;YAClB,MAAM,EAAE,MAAM;SACf,CACF,CAAC;QACF,OAAO,QAAQ,CAAC,IAAI,CAAC,IAAI,CAAC;IAC5B,CAAC;CACF"}