import { McpServer } from "@modelcontextprotocol/sdk/server/mcp.js";
import { z } from "zod";
import { NocoBaseClient } from "../client.js";
export async function registerSchemaTools(server, client) {
    // Get collection schema
    server.registerTool("get_collection_schema", {
        title: "Get Collection Schema",
        description: "Get the complete schema definition for a collection",
        inputSchema: {
            collection: z.string().describe("Name of the collection")
        }
    }, async ({ collection }) => {
        try {
            const collectionData = await client.getCollection(collection);
            let schema = `Schema for collection '${collection}':\n\n`;
            schema += `Name: ${collectionData.name}\n`;
            schema += `Title: ${collectionData.title || 'No title'}\n`;
            schema += `Description: ${collectionData.description || 'No description'}\n`;
            schema += `Auto-generated ID: ${collectionData.autoGenId ? 'Yes' : 'No'}\n`;
            schema += `Timestamps: ${collectionData.createdAt ? 'Created' : ''}${collectionData.updatedAt ? (collectionData.createdAt ? ', Updated' : 'Updated') : ''}\n`;
            schema += `User tracking: ${collectionData.createdBy ? 'Created by' : ''}${collectionData.updatedBy ? (collectionData.createdBy ? ', Updated by' : 'Updated by') : ''}\n`;
            schema += `Hidden: ${collectionData.hidden ? 'Yes' : 'No'}\n`;
            schema += `Inherits: ${collectionData.inherit ? 'Yes' : 'No'}\n\n`;
            if (collectionData.fields && collectionData.fields.length > 0) {
                schema += `Fields (${collectionData.fields.length}):\n\n`;
                collectionData.fields.forEach((field, index) => {
                    schema += `${index + 1}. ${field.name}\n`;
                    schema += `   Type: ${field.type}\n`;
                    schema += `   Interface: ${field.interface || 'None'}\n`;
                    schema += `   Description: ${field.description || 'No description'}\n`;
                    if (field.uiSchema) {
                        schema += `   UI Schema:\n`;
                        Object.entries(field.uiSchema).forEach(([key, value]) => {
                            schema += `     ${key}: ${typeof value === 'object' ? JSON.stringify(value) : value}\n`;
                        });
                    }
                    // Show other important field properties
                    const otherProps = Object.entries(field).filter(([key]) => !['key', 'name', 'type', 'interface', 'description', 'uiSchema', 'collectionName'].includes(key));
                    if (otherProps.length > 0) {
                        schema += `   Other properties:\n`;
                        otherProps.forEach(([key, value]) => {
                            if (value !== null && value !== undefined) {
                                schema += `     ${key}: ${typeof value === 'object' ? JSON.stringify(value) : value}\n`;
                            }
                        });
                    }
                    schema += '\n';
                });
            }
            else {
                schema += 'No fields defined.\n';
            }
            return {
                content: [{
                        type: "text",
                        text: schema
                    }]
            };
        }
        catch (error) {
            return {
                content: [{
                        type: "text",
                        text: `Error getting schema for collection '${collection}': ${error instanceof Error ? error.message : String(error)}`
                    }],
                isError: true
            };
        }
    });
    // List fields
    server.registerTool("list_fields", {
        title: "List Collection Fields",
        description: "List all fields in a collection",
        inputSchema: {
            collection: z.string().describe("Name of the collection")
        }
    }, async ({ collection }) => {
        try {
            const fields = await client.listFields(collection);
            let response = `Fields in collection '${collection}' (${fields.length} total):\n\n`;
            if (fields.length > 0) {
                fields.forEach((field, index) => {
                    response += `${index + 1}. ${field.name}\n`;
                    response += `   Type: ${field.type}\n`;
                    response += `   Interface: ${field.interface || 'None'}\n`;
                    if (field.description) {
                        response += `   Description: ${field.description}\n`;
                    }
                    response += '\n';
                });
            }
            else {
                response += 'No fields found.\n';
            }
            return {
                content: [{
                        type: "text",
                        text: response
                    }]
            };
        }
        catch (error) {
            return {
                content: [{
                        type: "text",
                        text: `Error listing fields for collection '${collection}': ${error instanceof Error ? error.message : String(error)}`
                    }],
                isError: true
            };
        }
    });
    // Create field
    server.registerTool("create_field", {
        title: "Create Field",
        description: "Add a new field to a collection",
        inputSchema: {
            collection: z.string().describe("Name of the collection"),
            name: z.string().describe("Name of the field"),
            type: z.string().describe("Field type (e.g., 'string', 'integer', 'boolean', 'date')"),
            interface: z.string().optional().describe("UI interface type (e.g., 'input', 'textarea', 'select')"),
            description: z.string().optional().describe("Field description"),
            uiSchema: z.any().optional().describe("UI schema configuration (JSON object)")
        }
    }, async ({ collection, name, type, interface: fieldInterface, description, uiSchema }) => {
        try {
            const fieldData = {
                name,
                type,
                description
            };
            if (fieldInterface) {
                fieldData.interface = fieldInterface;
            }
            if (uiSchema) {
                fieldData.uiSchema = uiSchema;
            }
            const field = await client.createField(collection, fieldData);
            return {
                content: [{
                        type: "text",
                        text: `Successfully created field '${field.name}' in collection '${collection}'`
                    }]
            };
        }
        catch (error) {
            return {
                content: [{
                        type: "text",
                        text: `Error creating field '${name}' in collection '${collection}': ${error instanceof Error ? error.message : String(error)}`
                    }],
                isError: true
            };
        }
    });
}
//# sourceMappingURL=schema.js.map