import { McpServer } from "@modelcontextprotocol/sdk/server/mcp.js";
import { NocoBaseClient } from "../client.js";
export declare const PAGE_TEMPLATES: {
    blank: {
        name: string;
        description: string;
        schema: {
            type: string;
            "x-component": string;
            "x-component-props": {};
            properties: {
                grid: {
                    type: string;
                    "x-component": string;
                    "x-initializer": string;
                };
            };
        };
    };
    table: {
        name: string;
        description: string;
        schema: (collectionName?: string) => {
            type: string;
            "x-component": string;
            properties: {
                grid: {
                    type: string;
                    "x-component": string;
                    properties: {
                        table: {
                            type: string;
                            "x-component": string;
                            "x-component-props": {
                                collection: string;
                                action: string;
                            };
                            properties: {
                                table: {
                                    type: string;
                                    "x-component": string;
                                    "x-component-props": {
                                        rowKey: string;
                                        rowSelection: {
                                            type: string;
                                        };
                                    };
                                };
                            };
                        };
                    };
                };
            };
        };
    };
    dashboard: {
        name: string;
        description: string;
        schema: {
            type: string;
            "x-component": string;
            properties: {
                grid: {
                    type: string;
                    "x-component": string;
                    "x-initializer": string;
                    properties: {
                        stats: {
                            type: string;
                            "x-component": string;
                            "x-component-props": {
                                title: string;
                            };
                        };
                    };
                };
            };
        };
    };
};
export declare function registerRouteTools(server: McpServer, client: NocoBaseClient): Promise<void>;
//# sourceMappingURL=routes.d.ts.map